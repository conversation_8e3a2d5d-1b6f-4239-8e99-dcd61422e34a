import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import TextWrapper from '../TextWrapper';
import CardContext from '../../context/CardContext';

// Mock the centralized design system tooltip
jest.mock('centralized-design-system/src/Tooltip', () => {
  return function MockTooltip({ children, message, hide, ariaLabel }) {
    return (
      <div data-testid="tooltip-wrapper" aria-label={ariaLabel}>
        {!hide && <div data-testid="tooltip-message">{message}</div>}
        {children}
      </div>
    );
  };
});

// Mock the window size hook
jest.mock('centralized-design-system/src/Utils/hooks', () => ({
  useWindowSize: () => ({ width: 1024, height: 768 })
}));

// Mock the config wrapper
jest.mock('../../hoc/configWrapper', () => (Component) => Component);

// Mock safeRender
jest.mock('edc-web-sdk/requests/renderingOptions', () => ({
  safeRender: (html) => html
}));

const mockCardContext = {
  card: { id: 'test-card-123' },
  isCurateTab: false
};

const renderWithContext = (component, contextValue = mockCardContext) => {
  return render(
    <CardContext.Provider value={contextValue}>
      {component}
    </CardContext.Provider>
  );
};

describe('TextWrapper', () => {
  beforeEach(() => {
    // Mock scrollWidth and offsetWidth for truncation detection
    Object.defineProperty(HTMLElement.prototype, 'scrollWidth', {
      configurable: true,
      value: 200
    });
    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
      configurable: true,
      value: 100
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without tooltip when text is not truncated', () => {
    const shortMessage = 'Short title';
    
    renderWithContext(
      <TextWrapper message={shortMessage} isStandaloneLayout={false} />
    );

    expect(screen.getByText(shortMessage)).toBeInTheDocument();
    expect(screen.queryByTestId('tooltip-message')).not.toBeInTheDocument();
  });

  it('shows tooltip when text is programmatically truncated for list cards', async () => {
    const longMessage = 'This is a very long title that should be truncated when displayed in a list card format';
    
    renderWithContext(
      <TextWrapper message={longMessage} isStandaloneLayout={false} listCard={true} />
    );

    await waitFor(() => {
      expect(screen.getByTestId('tooltip-message')).toBeInTheDocument();
      expect(screen.getByTestId('tooltip-message')).toHaveTextContent(longMessage);
    });
  });

  it('renders as h1 when isStandaloneLayout is true', () => {
    const message = 'Test title';
    
    renderWithContext(
      <TextWrapper message={message} isStandaloneLayout={true} />
    );

    const heading = screen.getByRole('heading', { level: 1 });
    expect(heading).toBeInTheDocument();
    expect(heading).toHaveTextContent(message);
  });

  it('renders as span when isStandaloneLayout is false', () => {
    const message = 'Test title';
    
    renderWithContext(
      <TextWrapper message={message} isStandaloneLayout={false} />
    );

    const span = screen.getByText(message);
    expect(span.tagName).toBe('SPAN');
  });

  it('applies correct CSS classes for list cards', () => {
    const message = 'Test title';
    
    renderWithContext(
      <TextWrapper message={message} isStandaloneLayout={false} listCard={true} />
    );

    const element = screen.getByText(message);
    expect(element).toHaveClass('list-card-title-text');
  });

  it('includes accessibility attributes in tooltip', async () => {
    const longMessage = 'This is a very long title that should be truncated when displayed in a list card format';
    
    renderWithContext(
      <TextWrapper message={longMessage} isStandaloneLayout={false} listCard={true} />
    );

    await waitFor(() => {
      const tooltipWrapper = screen.getByTestId('tooltip-wrapper');
      expect(tooltipWrapper).toHaveAttribute('aria-label', `Full title: ${longMessage}`);
    });
  });

  it('handles HTML content safely', () => {
    const htmlMessage = '<strong>Bold Title</strong>';
    
    renderWithContext(
      <TextWrapper message={htmlMessage} isStandaloneLayout={false} />
    );

    // The content should be rendered as HTML
    expect(screen.getByText('Bold Title')).toBeInTheDocument();
  });

  it('truncates text on small screens in curate tab', async () => {
    const message = 'This is a long title that should be truncated on small screens';
    const contextWithCurateTab = {
      ...mockCardContext,
      isCurateTab: true
    };

    // Mock small screen size
    jest.doMock('centralized-design-system/src/Utils/hooks', () => ({
      useWindowSize: () => ({ width: 400, height: 600 })
    }));

    renderWithContext(
      <TextWrapper message={message} isStandaloneLayout={true} />,
      contextWithCurateTab
    );

    await waitFor(() => {
      expect(screen.getByTestId('tooltip-message')).toBeInTheDocument();
    });
  });
});
