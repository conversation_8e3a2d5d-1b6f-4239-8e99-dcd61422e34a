import React, { useContext, useRef, useEffect, useState } from 'react';
import { string, bool } from 'prop-types';
import configWrapper from '../hoc/configWrapper';
import { safeRender } from 'edc-web-sdk/requests/renderingOptions';
import CardContext from '../context/CardContext';
import classnames from 'classnames';
import { useWindowSize } from 'centralized-design-system/src/Utils/hooks';
import { ANCHOR_TAG_WITH_ATTRIBUTES_REGEX } from '../../../../app/constants/regexConstants';
import { modifyAnchorTagsForNewTab } from '../utils/modifyAnchorTagsForNewTab';
import Tooltip from 'centralized-design-system/src/Tooltip';
import './TextWrapper.scss';

// Utility function to detect if text is truncated
const isTruncated = (element) => {
  if (!element) return false;
  return element.scrollWidth > element.offsetWidth || element.scrollHeight > element.offsetHeight;
};

const TextWrapper = ({ message, isStandaloneLayout, listCard = false, openInNewWindow }) => {
  const CardContextData = useContext(CardContext);
  const textRef = useRef(null);
  const [showTooltip, setShowTooltip] = useState(false);
  const [isTextTruncated, setIsTextTruncated] = useState(false);

  let updatedMessage = message;
  const windowSizeObject = useWindowSize();
  const screenSize = windowSizeObject.width;
  const cardHeadingId = CardContextData?.card?.id + '-title';

  // Track if text was programmatically truncated
  let isProgrammaticallyTruncated = false;

  if (
    (listCard && message?.length > 65) ||
    (isStandaloneLayout && CardContextData.isCurateTab && screenSize < 550)
  ) {
    updatedMessage = message.substring(0, 62);
    updatedMessage = `${updatedMessage}...`;
    isProgrammaticallyTruncated = true;
  }

  // Add or remove target="_blank" based on config
  updatedMessage = updatedMessage?.replace(
    ANCHOR_TAG_WITH_ATTRIBUTES_REGEX,
    modifyAnchorTagsForNewTab(openInNewWindow)
  );

  // Check for truncation after component mounts and updates
  useEffect(() => {
    const checkTruncation = () => {
      if (textRef.current) {
        const isCssTruncated = isTruncated(textRef.current);
        const shouldShowTooltip = isProgrammaticallyTruncated || isCssTruncated;
        setIsTextTruncated(shouldShowTooltip);
        setShowTooltip(shouldShowTooltip);
      }
    };

    // Check immediately and after a short delay to ensure DOM is ready
    checkTruncation();
    const timeoutId = setTimeout(checkTruncation, 100);

    return () => clearTimeout(timeoutId);
  }, [message, updatedMessage, screenSize, isProgrammaticallyTruncated]);

  // Get the original message for tooltip display
  const tooltipMessage = message;

  // Render content with tooltip wrapper if text is truncated
  const renderContent = () => {
    if (isStandaloneLayout) {
      return (
        <h1
          ref={textRef}
          className={classnames({
            'card-title-header': isStandaloneLayout,
            'list-card-title-text': listCard
          })}
          dangerouslySetInnerHTML={{
            __html: safeRender(updatedMessage)
          }}
          id={cardHeadingId}
        />
      );
    } else {
      return (
        <span className="font-weight-400 font-size-l" id={cardHeadingId}>
          <span
            ref={textRef}
            className={classnames({ 'list-card-title-text': listCard })}
            dangerouslySetInnerHTML={{
              __html: safeRender(updatedMessage)
            }}
          />
        </span>
      );
    }
  };

  // Wrap with tooltip if text is truncated
  if (isTextTruncated && tooltipMessage) {
    return (
      <Tooltip
        message={tooltipMessage}
        isHtmlIncluded={true}
        pos="top"
        hide={!showTooltip}
        customClass="smartcard-title-tooltip"
        tabIndex={0}
        ariaLabel={`Full title: ${tooltipMessage}`}
      >
        {renderContent()}
      </Tooltip>
    );
  }

  return renderContent();
};

TextWrapper.propTypes = {
  message: string,
  isStandaloneLayout: bool,
  listCard: bool,
  openInNewWindow: bool
};

export default configWrapper(TextWrapper);
