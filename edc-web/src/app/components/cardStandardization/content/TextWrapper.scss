@import '~styles/base';
@import '~centralized-design-system/src/Styles/_variables.scss';

// Smartcard title tooltip specific styles
.smartcard-title-tooltip {
  // Ensure tooltip doesn't interfere with card layout
  .ed-tooltip {
    display: inline-block;
    width: 100%;
    
    // Ensure the tooltip content maintains proper styling
    .tooltip-msg {
      max-width: rem-calc(400);
      word-break: break-word;
      white-space: normal;
      line-height: 1.4;
      
      // Handle HTML content in tooltips safely
      p, span, div {
        margin: 0;
        padding: 0;
        font-size: inherit;
        line-height: inherit;
      }
    }
  }
  
  // Ensure focus states are visible for accessibility
  &:focus {
    outline: 2px solid var(--ed-focus-color, #0066cc);
    outline-offset: 2px;
  }
  
  // Responsive tooltip positioning
  @include max-screen-width($breakpoint-sm) {
    .tooltip-msg {
      max-width: rem-calc(280);
    }
  }
}

// Ensure truncated text elements have proper cursor indication
.list-card-title-text,
.card-title-header {
  &.truncated {
    cursor: help;
  }
}
