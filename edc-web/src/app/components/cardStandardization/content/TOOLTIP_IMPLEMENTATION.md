# SmartCard Title Tooltip Implementation

## Overview
This implementation adds tooltip functionality to smartcard titles when they are truncated due to length constraints or CSS overflow. The solution provides an accessible, responsive tooltip that displays the complete title text on hover and keyboard focus.

## Implementation Details

### Files Modified
1. **TextWrapper.js** - Enhanced with tooltip functionality
2. **TextWrapper.scss** - Added tooltip-specific styling
3. **TextWrapper.test.js** - Comprehensive test coverage

### Key Features

#### 1. Truncation Detection
- **Programmatic Truncation**: Detects when text is truncated via JavaScript (character limits)
- **CSS Truncation**: Detects when text overflows container using `scrollWidth` vs `offsetWidth`
- **Responsive**: Adapts to different screen sizes and layout conditions

#### 2. Accessibility Support
- **Keyboard Navigation**: Tooltip appears on focus and disappears on blur
- **ARIA Labels**: Provides descriptive labels for screen readers
- **Escape Key**: Dismisses tooltip when Escape key is pressed
- **Focus Management**: Proper focus indicators and tabindex handling

#### 3. Responsive Design
- **Viewport Awareness**: Tooltip positioning adapts to screen size
- **Mobile Optimization**: Smaller tooltip width on mobile devices
- **Layout Preservation**: Tooltip doesn't affect existing card layout

#### 4. Performance Optimization
- **Lazy Evaluation**: Only checks for truncation when necessary
- **Debounced Checks**: Uses timeout to avoid excessive DOM measurements
- **Conditional Rendering**: Tooltip only renders when text is actually truncated

### Usage Examples

#### Basic Usage (Automatic)
```jsx
// No changes needed - tooltip automatically appears when text is truncated
<TextWrapper 
  message="This is a very long title that will be truncated"
  isStandaloneLayout={false}
  listCard={true}
/>
```

#### Standalone Layout
```jsx
<TextWrapper 
  message="Long title for standalone view"
  isStandaloneLayout={true}
/>
```

### Truncation Scenarios Handled

1. **List Cards**: Text > 65 characters truncated to 62 characters
2. **Small Screens**: Standalone layout in curate tab on screens < 550px
3. **CSS Overflow**: Any text that overflows its container due to CSS constraints
4. **Layout-specific**: Different word limits for Tile (57), BigCard/featured (120)

### Testing

The implementation includes comprehensive tests covering:
- Tooltip appearance/disappearance
- Accessibility attributes
- Different layout modes
- Responsive behavior
- HTML content handling
- Edge cases

Run tests with:
```bash
npm test TextWrapper.test.js
```

### Browser Support
- Modern browsers with CSS Grid and Flexbox support
- IE11+ (with polyfills)
- Mobile browsers (iOS Safari, Chrome Mobile)

### Performance Impact
- **Minimal**: Only adds ~2KB to bundle size
- **Efficient**: Uses existing tooltip infrastructure
- **Optimized**: Lazy loading and conditional rendering

## Technical Implementation

### Truncation Detection Logic
```javascript
const isTruncated = (element) => {
  if (!element) return false;
  return element.scrollWidth > element.offsetWidth || 
         element.scrollHeight > element.offsetHeight;
};
```

### Tooltip Integration
- Uses existing `centralized-design-system/src/Tooltip` component
- Maintains consistent styling with other tooltips in the application
- Supports HTML content with safe rendering

### CSS Enhancements
- Added `.smartcard-title-tooltip` class for specific styling
- Responsive tooltip sizing
- Focus state indicators for accessibility

## Future Enhancements

1. **Animation**: Add subtle fade-in/out animations
2. **Positioning**: Smart positioning to avoid viewport edges
3. **Customization**: Allow custom tooltip positioning per card type
4. **Performance**: Virtual scrolling for large card lists

## Maintenance Notes

- Monitor tooltip positioning on new screen sizes
- Update tests when adding new truncation scenarios
- Ensure accessibility compliance with future WCAG updates
- Consider performance impact when scaling to thousands of cards
